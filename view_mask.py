import matplotlib.pyplot as plt
import cv2
import numpy as np

def visualize_image(image_path):
    """
    Visualise une image PNG avec normalisation des valeurs pour une meilleure visibilité.
    
    Args:
        image_path: Chemin vers le fichier image
    """
    # Charger l'image
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if img is None:
        print(f"❌ Impossible de charger l'image: {image_path}")
        return
    
    # Afficher les valeurs uniques pour debug
    unique_vals = np.unique(img)
    print(f"Valeurs uniques dans l'image: {unique_vals}")

    # Créer une image couleur RGB
    colored_img = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.uint8)

    # Mapping des valeurs vers des couleurs (RGB pour matplotlib)
    # Même mapping que view_png_folder.py
    if set(unique_vals).issubset({0, 1, 2, 3, 4}):
        # Mapping pour les nouvelles valeurs (0-4) - même ordre que view_png_folder
        color_mapping = {
            0: [0, 0, 0],        # background -> noir
            1: [0, 0, 255],      # frontwall -> bleu
            2: [0, 255, 0],      # backwall -> vert
            3: [255, 0, 0],      # flaw/défaut -> rouge
            4: [255, 255, 0]     # indication -> jaune
        }
        mapping_type = "nouvelles valeurs (0-4)"
    else:
        # Mapping pour les anciennes valeurs (0, 29, 76, 125, 149)
        color_mapping = {
            0: [0, 0, 0],        # background -> noir
            29: [0, 0, 255],     # frontwall -> bleu
            76: [0, 255, 0],     # backwall -> vert
            125: [255, 0, 0],    # flaw/défaut -> rouge
            149: [255, 255, 0]   # indication -> jaune
        }
        mapping_type = "anciennes valeurs (0, 29, 76, 125, 149)"

    print(f"Type de mapping détecté: {mapping_type}")

    # Appliquer le mapping couleur avec debug
    for old_val, color in color_mapping.items():
        mask = img == old_val
        count = np.sum(mask)
        if count > 0:
            print(f"Valeur {old_val}: {count} pixels trouvés -> couleur {color}")
            colored_img[mask] = color
        else:
            print(f"Valeur {old_val}: aucun pixel trouvé")

    # Vérifier si l'image colorée contient des couleurs
    if np.all(colored_img == 0):
        print("⚠️ L'image colorée est entièrement noire!")
        # Créer une image de test avec des couleurs pour toutes les valeurs présentes
        for i, val in enumerate(unique_vals):
            if val not in color_mapping:
                # Assigner une couleur par défaut aux valeurs non mappées
                default_colors = [[128, 128, 128], [255, 0, 255], [0, 255, 255], [255, 128, 0]]
                color = default_colors[i % len(default_colors)]
                mask = img == val
                colored_img[mask] = color
                print(f"Valeur non mappée {val}: assignée à la couleur {color}")
    else:
        print(f"✅ Image colorée créée avec succès")
    
    # Créer la figure avec deux sous-graphiques
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
    
    # Afficher l'image originale
    im1 = ax1.imshow(img, cmap='gray')
    ax1.set_title('Image originale')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, label='Valeurs originales')
    
    # Afficher l'image colorée
    im2 = ax2.imshow(colored_img)
    ax2.set_title('Image colorée par classe')
    ax2.axis('off')

    # Ajouter une légende pour les couleurs selon le type de mapping
    if "nouvelles" in mapping_type:
        legend_labels = [
            "0: background (noir)",
            "1: frontwall (bleu)",
            "2: backwall (vert)",
            "3: flaw (rouge)",
            "4: indication (jaune)"
        ]
    else:
        legend_labels = [
            "0: background (noir)",
            "29: frontwall (bleu)",
            "76: backwall (vert)",
            "125: flaw (rouge)",
            "149: indication (jaune)"
        ]
    legend_text = "\n".join(legend_labels)
    plt.figtext(0.5, 0.01, f"Mapping des couleurs:\n{legend_text}",
                ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    import sys

    # Permettre de spécifier le chemin en argument ou utiliser un chemin par défaut
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    else:
        # Chemin vers une image de test par défaut
        image_path = "test_labels/test_label_000000.png"

    print(f"Visualisation de l'image: {image_path}")
    # Visualiser l'image
    visualize_image(image_path)