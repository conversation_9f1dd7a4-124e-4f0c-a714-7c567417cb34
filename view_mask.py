import matplotlib.pyplot as plt
import cv2
import numpy as np

def visualize_image(image_path):
    """
    Visualise une image PNG avec normalisation des valeurs pour une meilleure visibilité.
    
    Args:
        image_path: Chemin vers le fichier image
    """
    # Charger l'image
    img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if img is None:
        print(f"❌ Impossible de charger l'image: {image_path}")
        return
    
    # Afficher les valeurs uniques pour debug
    unique_vals = np.unique(img)
    print(f"Valeurs uniques dans l'image: {unique_vals}")
    
    # Créer une image couleur RGB
    colored_img = np.zeros((img.shape[0], img.shape[1], 3), dtype=np.uint8)

    # Mapping des valeurs vers des couleurs
    color_mapping = {
        0: [0, 0, 0],        # background -> noir
        29: [0, 0, 255],     # frontwall -> bleu
        76: [255, 0, 0],     # flaw/défaut -> rouge
        125: [255, 255, 0],  # indication -> jaune
        149: [0, 255, 0]     # backwall -> vert
    }

    # Appliquer le mapping couleur
    for old_val, color in color_mapping.items():
        mask = img == old_val
        colored_img[mask] = color
    
    # Créer la figure avec deux sous-graphiques
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
    
    # Afficher l'image originale
    im1 = ax1.imshow(img, cmap='gray')
    ax1.set_title('Image originale')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, label='Valeurs originales')
    
    # Afficher l'image normalisée
    im2 = ax2.imshow(normalized, cmap='gray')
    ax2.set_title('Image normalisée')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, label='Valeurs normalisées')
    
    # Ajouter une légende pour les valeurs
    legend_text = "\n".join([f"{k} -> {v}" for k, v in value_mapping.items()])
    plt.figtext(0.5, 0.01, f"Mapping des valeurs:\n{legend_text}", 
                ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # Chemin vers votre image
    image_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset012_test4labeldifferent\labelsTr_backup\0001.png"
    
    # Visualiser l'image
    visualize_image(image_path) 